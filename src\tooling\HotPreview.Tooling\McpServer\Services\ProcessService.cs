using HotPreview.Tooling.McpServer.Helpers;
using HotPreview.Tooling.McpServer.Interfaces;

namespace HotPreview.Tooling.McpServer.Services;

/// <summary>
/// Default implementation of IProcessService that uses the Process helper class.
/// </summary>
public class ProcessService : IProcessService
{
    /// <summary>
    /// Executes a shell command and returns the standard output as a string.
    /// </summary>
    /// <param name="command">The shell command to be executed.</param>
    /// <returns>The output from the executed command.</returns>
    /// <exception cref="Exception">
    /// Thrown when an error occurs during the command execution process.
    /// </exception>
    public string ExecuteCommand(string command)
    {
        return Process.ExecuteCommand(command);
    }

    /// <summary>
    /// Starts a new process to execute the specified shell command.
    /// </summary>
    /// <param name="command">The shell command to be executed.</param>
    /// <returns>
    /// The <see cref="System.Diagnostics.Process"/> instance representing the started process.
    /// </returns>
    /// <exception cref="Exception">
    /// Thrown when an error occurs during the process startup.
    /// </exception>
    public System.Diagnostics.Process StartProcess(string command)
    {
        return Process.StartProcess(command);
    }
}
